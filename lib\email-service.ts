import FormData from 'form-data';
import Mailgun from 'mailgun.js';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
}

export interface EmailServiceConfig {
  apiKey: string;
  domain: string;
  from: string;
  region?: 'us' | 'eu';
}

class EmailService {
  private mg: any;
  private config: EmailServiceConfig;

  constructor(config: EmailServiceConfig) {
    this.config = config;
    const mailgun = new Mailgun(FormData);
    
    this.mg = mailgun.client({
      username: 'api',
      key: config.apiKey,
      url: config.region === 'eu' ? 'https://api.eu.mailgun.net' : 'https://api.mailgun.net'
    });
  }

  async sendEmail(options: EmailOptions): Promise<any> {
    try {
      const emailData = {
        from: options.from || this.config.from,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: options.subject,
        html: options.html,
      };

      console.log('Sending email via Mailgun:', {
        domain: this.config.domain,
        to: emailData.to,
        subject: emailData.subject,
        from: emailData.from
      });

      const result = await this.mg.messages.create(this.config.domain, emailData);
      console.log('Email sent successfully:', result);
      return result;
    } catch (error) {
      console.error('Failed to send email via Mailgun:', error);
      throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async sendMultipleEmails(emails: EmailOptions[]): Promise<any[]> {
    try {
      const results = await Promise.all(
        emails.map(email => this.sendEmail(email))
      );
      return results;
    } catch (error) {
      console.error('Failed to send multiple emails:', error);
      throw error;
    }
  }
}

// Create a singleton instance
let emailService: EmailService | null = null;

export function getEmailService(): EmailService {
  if (!emailService) {
    const config: EmailServiceConfig = {
      apiKey: process.env.MAILGUN_API_KEY || '',
      domain: process.env.MAILGUN_DOMAIN || '',
      from: process.env.MAILGUN_FROM || 'UpZera <<EMAIL>>',
      region: (process.env.MAILGUN_REGION as 'us' | 'eu') || 'eu'
    };

    if (!config.apiKey || !config.domain) {
      throw new Error('Mailgun configuration is missing. Please set MAILGUN_API_KEY and MAILGUN_DOMAIN environment variables.');
    }

    emailService = new EmailService(config);
  }

  return emailService;
}

// Internationalization helper for email templates
async function getEmailTranslations(locale: string = 'en') {
  try {
    const messages = await import(`../messages/${locale}.json`);
    return messages.default.EmailTemplates;
  } catch (error) {
    // Fallback to English if locale not found
    const messages = await import(`../messages/en.json`);
    return messages.default.EmailTemplates;
  }
}

// Helper function to replace placeholders in translation strings
function replacePlaceholders(text: string, replacements: Record<string, string>): string {
  return text.replace(/\{(\w+)\}/g, (match, key) => replacements[key] || match);
}

// Email template utilities
export async function createContactFormNotificationEmail(data: {
  name: string;
  email: string;
  service: string;
  message: string;
  locale?: string;
}): Promise<string> {
  const t = await getEmailTranslations(data.locale || 'en');
  const currentYear = new Date().getFullYear().toString();
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); padding:28px; text-align:center; border-bottom:1px solid #f472b6;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="${t.common.logoAlt}" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="color: #7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          ${t.contactForm.notificationTitle}
        </h1>

        <p style="color:#4b5563; line-height:1.6; margin:0 0 24px 0; font-size:16px;">
          ${t.contactForm.notificationDescription}
        </p>

        <div style="background:#f8fafc; padding:24px; border-radius:8px; margin-bottom:24px; border-left:4px solid #7e22ce;">
          <h2 style="color: #7e22ce; font-size:18px; margin:0 0 16px 0; font-weight:bold;">${t.contactForm.contactDetails}</h2>
          <p style="margin:8px 0; color:#4b5563;"><strong style="color:#374151;">${t.contactForm.name}:</strong> ${data.name}</p>
          <p style="margin:8px 0; color:#4b5563;"><strong style="color:#374151;">${t.contactForm.email}:</strong> ${data.email}</p>
          <p style="margin:8px 0; color:#4b5563;"><strong style="color:#374151;">${t.contactForm.service}:</strong> ${data.service}</p>
        </div>

        <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); padding:24px; border-radius:8px; border-left:4px solid #ec4899;">
          <h2 style="color: #7e22ce; font-size:18px; margin:0 0 16px 0; font-weight:bold;">${t.contactForm.message}</h2>
          <p style="color:#4b5563; line-height:1.6; margin:0; white-space:pre-wrap;">${data.message}</p>
        </div>
      </div>

      <!-- Footer -->
      <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); padding:18px; text-align:center; border-top:1px solid #f472b6; color:#6b21a8; font-size:13px;">
        ${replacePlaceholders(t.common.copyrightText, { year: currentYear })}
      </div>
    </div>
  `;
}

export async function createContactFormConfirmationEmail(name: string, locale: string = 'en'): Promise<string> {
  const t = await getEmailTranslations(locale);
  const currentYear = new Date().getFullYear().toString();
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); padding:28px; text-align:center; border-bottom:1px solid #f472b6;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="${t.common.logoAlt}" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          ${t.contactForm.confirmationTitle}
        </h1>

        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          ${replacePlaceholders(t.contactForm.confirmationGreeting, { name })}
        </p>

        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          ${t.contactForm.confirmationMessage}
        </p>

        <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); padding:24px; border-radius:8px; margin:24px 0; border-left:4px solid #ec4899;">
          <h3 style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-size:18px; margin:0 0 16px 0;">${t.contactForm.confirmationNextSteps}</h3>
          <ul style="color:#4b5563; margin:0; padding-left:20px; line-height:1.6;">
            <li style="margin-bottom:8px;">${t.contactForm.confirmationStep1}</li>
            <li style="margin-bottom:8px;">${t.contactForm.confirmationStep2}</li>
            <li style="margin-bottom:0;">${t.contactForm.confirmationStep3}</li>
          </ul>
        </div>

        <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); padding:24px; border-radius:8px; margin:24px 0; border-left:4px solid #ec4899; text-align:center;">
          <p style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-weight:bold; margin:0 0 8px 0; font-size:16px;">${t.contactForm.immediateAssistance}</p>
          <p style="color:#4b5563; margin:0; font-size:14px;">${t.contactForm.contactUsAt} <EMAIL></p>
        </div>

        <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
          ${t.contactForm.bestRegards}<br>
          <strong style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce;">${t.contactForm.teamSignature}</strong>
        </p>
      </div>

      <!-- Footer -->
      <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); padding:18px; text-align:center; border-top:1px solid #f472b6; color:#6b21a8; font-size:13px;">
        ${replacePlaceholders(t.common.copyrightText, { year: currentYear })}
      </div>
    </div>
  `;
}

export async function createNewsletterNotificationEmail(email: string, locale: string = 'en'): Promise<string> {
  const t = await getEmailTranslations(locale);
  const currentYear = new Date().getFullYear().toString();
  const subscriptionDate = new Date().toLocaleDateString(locale === 'lt' ? 'lt-LT' : 'en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); padding:28px; text-align:center; border-bottom:1px solid #f472b6;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="${t.common.logoAlt}" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="color: #7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          ${t.newsletter.notificationTitle}
        </h1>

        <p style="color:#4b5563; line-height:1.6; margin:0 0 24px 0; font-size:16px;">
          ${t.newsletter.notificationDescription}
        </p>

        <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); padding:24px; border-radius:8px; border-left:4px solid #ec4899;">
          <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
            <strong style="color: #7e22ce; font-weight:bold;">${t.newsletter.subscriberEmail}:</strong> ${email}
          </p>
          <p style="color:#6b7280; margin:12px 0 0 0; font-size:14px;">
            ${subscriptionDate}
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); padding:18px; text-align:center; border-top:1px solid #f472b6; color:#6b21a8; font-size:13px;">
        ${replacePlaceholders(t.common.copyrightText, { year: currentYear })}
      </div>
    </div>
  `;
}

export async function createNewsletterConfirmationEmail(locale: string = 'en'): Promise<string> {
  const t = await getEmailTranslations(locale);
  const currentYear = new Date().getFullYear().toString();

  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); padding:28px; text-align:center; border-bottom:1px solid #f472b6;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="${t.common.logoAlt}" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          ${t.newsletter.confirmationTitle}
        </h1>

        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          ${t.newsletter.confirmationMessage}
        </p>

   

        <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
          ${t.contactForm.bestRegards}<br>
          <strong style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce;">${t.contactForm.teamSignature}</strong>
        </p>
      </div>

      <!-- Footer -->
      <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); padding:18px; text-align:center; border-top:1px solid #f472b6; color:#6b21a8; font-size:13px;">
        ${replacePlaceholders(t.common.copyrightText, { year: currentYear })}

      </div>
    </div>
  `;
}

// Support Ticket Email Templates
export async function createSupportTicketNotificationEmail(data: {
  ticketNumber: string;
  name: string;
  email: string;
  problemDescription: string;
  conversationHistory: string;
  locale?: string;
}): Promise<string> {
  const t = await getEmailTranslations(data.locale || 'en');
  const currentDate = new Date().toLocaleString(data.locale === 'lt' ? 'lt-LT' : 'en-US');
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); padding:28px; text-align:center; border-bottom:1px solid #f472b6;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="${t.common.logoAlt}" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Main Content -->
      <div style="padding:32px; background:#ffffff;">
        <h1 style="color: #7e22ce; font-size:28px; font-weight:bold; margin:0 0 20px 0; text-align:center;">🎫 ${t.supportTicket.notificationTitle}</h1>

        <p style="color:#4b5563; line-height:1.6; margin:0 0 24px 0; font-size:16px;">
          ${t.supportTicket.notificationDescription}
        </p>

        <!-- Ticket Info Box -->
        <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); border:1px solid #f472b6; border-radius:8px; padding:20px; margin:0 0 24px 0;">
          <h2 style="color: #7e22ce; font-size:20px; font-weight:bold; margin:0 0 12px 0;">${t.supportTicket.ticketNumber} #${data.ticketNumber}</h2>
          <p style="color:#6b21a8; font-size:14px; margin:0;">${currentDate}</p>
        </div>

        <!-- Customer Details -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">${t.supportTicket.customerInfo}</h3>
          <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); border-radius:6px; padding:16px; border-left:4px solid #ec4899;">
            <p style="color:#374151; font-size:16px; margin:0 0 8px 0;"><strong>${t.contactForm.name}:</strong> ${data.name}</p>
            <p style="color:#374151; font-size:16px; margin:0;"><strong>${t.contactForm.email}:</strong> ${data.email}</p>
          </div>
        </div>

        <!-- Problem Description -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">${t.supportTicket.problemDescription}</h3>
          <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); border-radius:6px; padding:16px; border-left:4px solid #ec4899;">
            <p style="color:#374151; font-size:16px; line-height:1.6; margin:0;">${data.problemDescription}</p>
          </div>
        </div>

        ${data.conversationHistory ? `
        <!-- Conversation History -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">${t.supportTicket.conversationHistory}</h3>
          <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); border:1px solid #f472b6; border-radius:8px; padding:16px;">
            <pre style="color:#475569; font-size:12px; line-height:1.5; margin:0; white-space:pre-wrap; font-family:'Courier New', monospace; background:transparent; word-wrap:break-word; overflow-wrap:break-word;">${data.conversationHistory}</pre>
          </div>
        </div>
        ` : `
        <!-- No Conversation History -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">${t.supportTicket.conversationHistory}</h3>
          <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); border:1px solid #f472b6; border-radius:8px; padding:16px;">
            <p style="color:#6b7280; font-size:14px; margin:0; font-style:italic;">${t.supportTicket.noConversationHistory}</p>
          </div>
        </div>
        `}
      </div>

      <!-- Footer -->
      <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); padding:18px; text-align:center; border-top:1px solid #f472b6; color:#6b21a8; font-size:13px;">
        ${replacePlaceholders(t.common.copyrightText, { year: new Date().getFullYear().toString() })}
      </div>
    </div>
  `;
}

export async function createSupportTicketConfirmationEmail(data: {
  name: string;
  ticketNumber: string;
  locale?: string;
}): Promise<string> {
  const t = await getEmailTranslations(data.locale || 'en');
  const currentYear = new Date().getFullYear().toString();
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); padding:28px; text-align:center; border-bottom:1px solid #f472b6;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="${t.common.logoAlt}" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Main Content -->
      <div style="padding:32px; background:#ffffff;">
        <h1 style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-size:28px; font-weight:bold; margin:0 0 20px 0; text-align:center;">✅ ${t.supportTicket.confirmationTitle}</h1>

        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          ${replacePlaceholders(t.supportTicket.confirmationGreeting, { name: data.name })}
        </p>

        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          ${t.supportTicket.confirmationMessage}
        </p>

        <!-- Ticket Info Box -->
        <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); border:1px solid #f472b6; border-radius:8px; padding:20px; margin:0 0 24px 0; text-align:center;">
          <h2 style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-size:24px; font-weight:bold; margin:0 0 8px 0;">${t.supportTicket.yourTicketNumber}</h2>
          <div style="background:#ffffff; border:2px solid #ec4899; border-radius:6px; padding:12px; display:inline-block;">
            <span style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-size:20px; font-weight:bold; font-family:monospace;">#${data.ticketNumber}</span>
          </div>
          <p style="color:#6b21a8; font-size:14px; margin:12px 0 0 0;">${t.supportTicket.trackTicketDescription}</p>
        </div>

        <!-- Ticket Details -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 16px 0;">${t.supportTicket.ticketDetails}</h3>
          <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); border-radius:6px; padding:20px; border-left:4px solid #ec4899;">
            <p style="color:#374151; font-size:16px; margin:0 0 8px 0;"><strong>${t.supportTicket.status}:</strong> ${t.supportTicket.statusOpen}</p>
          </div>
        </div>

        <!-- What Happens Next -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 16px 0;">${t.supportTicket.nextSteps}</h3>
          <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); border-radius:6px; padding:20px; border-left:4px solid #ec4899;">
            <div style="margin-bottom:16px;">
              <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); color:#ffffff; border-radius:50%; width:24px; height:24px; display:inline-flex; align-items:center; justify-content:center; font-weight:bold; font-size:12px; margin-right:12px;">1</div>
              <span style="color:#374151; font-size:14px; line-height:1.5;">${t.supportTicket.nextStep1}</span>
            </div>
            <div style="margin-bottom:16px;">
              <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); color:#ffffff; border-radius:50%; width:24px; height:24px; display:inline-flex; align-items:center; justify-content:center; font-weight:bold; font-size:12px; margin-right:12px;">2</div>
              <span style="color:#374151; font-size:14px; line-height:1.5;">${t.supportTicket.nextStep2}</span>
            </div>
            <div>
              <div style="background: linear-gradient(135deg, #7e22ce, #ec4899); color:#ffffff; border-radius:50%; width:24px; height:24px; display:inline-flex; align-items:center; justify-content:center; font-weight:bold; font-size:12px; margin-right:12px;">3</div>
              <span style="color:#374151; font-size:14px; line-height:1.5;">${t.supportTicket.nextStep3}</span>
            </div>
          </div>
        </div>

        <!-- Contact Info -->
        <div style="background: linear-gradient(135deg, #f8fafc, #fdf2f8); padding:24px; border-radius:8px; margin:24px 0; border-left:4px solid #ec4899; text-align:center;">
          <p style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce; font-weight:bold; margin:0 0 8px 0; font-size:16px;">${t.contactForm.immediateAssistance}</p>
          <p style="color:#4b5563; margin:0; font-size:14px;">${t.contactForm.contactUsAt} <EMAIL></p>
        </div>

        <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
          ${t.contactForm.bestRegards}<br>
          <strong style="background: linear-gradient(135deg, #7e22ce, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; color: #7e22ce;">${t.contactForm.teamSignature}</strong>
        </p>
      </div>

      <!-- Footer -->
      <div style="background: linear-gradient(135deg, #f5f3ff, #fdf2f8); padding:18px; text-align:center; border-top:1px solid #f472b6; color:#6b21a8; font-size:13px;">
        ${replacePlaceholders(t.common.copyrightText, { year: currentYear })}
      </div>
    </div>
  `;
}
