// Test script for AI classification improvements
// This script tests various yes/no responses in both English and Lithuanian

const testCases = [
  // Lithuanian test cases that should be classified as "yes" (ready to end conversation)
  { input: "i<PERSON><PERSON><PERSON><PERSON>", expected: "yes", language: "lt", description: "Lithuanian: I'm convinced/sure" },
  { input: "taip", expected: "yes", language: "lt", description: "Lithuanian: yes" },
  { input: "tikrai", expected: "yes", language: "lt", description: "Lithuanian: really/definitely" },
  { input: "ž<PERSON><PERSON>", expected: "yes", language: "lt", description: "Lithuanian: of course" },
  { input: "gerai", expected: "yes", language: "lt", description: "Lithuanian: good/okay" },
  { input: "įsitikinęs", expected: "yes", language: "lt", description: "Lithuanian: convinced" },
  
  // Lithuanian test cases that should be classified as "no" (want to continue)
  { input: "ne", expected: "no", language: "lt", description: "Lithuanian: no" },
  { input: "galbūt", expected: "no", language: "lt", description: "Lithuanian: maybe" },
  { input: "ne<PERSON><PERSON>u", expected: "no", language: "lt", description: "Lithuanian: I don't know" },
  { input: "nesu tikras", expected: "no", language: "lt", description: "Lithuanian: I'm not sure" },
  { input: "palauk", expected: "no", language: "lt", description: "Lithuanian: wait" },
  
  // English test cases that should be classified as "yes" (ready to end conversation)
  { input: "yes", expected: "yes", language: "en", description: "English: yes" },
  { input: "sure", expected: "yes", language: "en", description: "English: sure" },
  { input: "absolutely", expected: "yes", language: "en", description: "English: absolutely" },
  { input: "confirmed", expected: "yes", language: "en", description: "English: confirmed" },
  { input: "that's right", expected: "yes", language: "en", description: "English: that's right" },
  
  // English test cases that should be classified as "no" (want to continue)
  { input: "no", expected: "no", language: "en", description: "English: no" },
  { input: "not sure", expected: "no", language: "en", description: "English: not sure" },
  { input: "maybe", expected: "no", language: "en", description: "English: maybe" },
  { input: "wait", expected: "no", language: "en", description: "English: wait" },
  { input: "actually", expected: "no", language: "en", description: "English: actually" },
  
  // Ambiguous cases that should lean towards "no" (continue conversation)
  { input: "hmm", expected: "no", language: "both", description: "Ambiguous: hmm" },
  { input: "let me think", expected: "no", language: "en", description: "Ambiguous: let me think" },
  { input: "dar pagalvosiu", expected: "no", language: "lt", description: "Lithuanian: I'll think more" }
];

// Mock conversation context for testing
const mockConversationContext = [
  "Bot: Sveiki! Kaip galiu jums padėti šiandien?",
  "User: Noriu sužinoti apie jūsų paslaugas",
  "Bot: Puiku! Mes teikiame web development ir chatbot paslaugas.",
  "User: Ačiū už informaciją",
  "Bot: Ar dar ko nors reikia?",
  "User: Ne, ačiū",
  "Bot: Suprantu! Prieš baigdami pokalbį, ar tikrai jums nereikia jokios kitos pagalbos šiandien? 🤔"
];

console.log("🧪 AI Classification Test Cases");
console.log("================================");
console.log(`Testing ${testCases.length} different responses...`);
console.log("");

// Instructions for manual testing
console.log("📋 MANUAL TESTING INSTRUCTIONS:");
console.log("1. Start the chatbot application");
console.log("2. Navigate through a conversation to reach the END_CONVERSATION_CONFIRM state");
console.log("3. Try each of the test inputs below and verify the classification");
console.log("4. Check the browser console for classification logs");
console.log("");

console.log("🔍 TEST CASES TO TRY:");
testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. Input: "${testCase.input}"`);
  console.log(`   Expected: ${testCase.expected}`);
  console.log(`   Language: ${testCase.language}`);
  console.log(`   Description: ${testCase.description}`);
  console.log("");
});

console.log("🎯 WHAT TO LOOK FOR:");
console.log("- Check browser console for: '🎯 AI classified yes/no as: [result]'");
console.log("- Check browser console for: '🎯 Enhanced AI classified as: [result]'");
console.log("- Verify the conversation flow matches the expected behavior");
console.log("- 'yes' should end the conversation");
console.log("- 'no' should show the main menu");
console.log("- 'unknown' should trigger contextual response");

console.log("");
console.log("🔧 DEBUGGING TIPS:");
console.log("- Open browser DevTools and check the Console tab");
console.log("- Look for classification logs during the conversation");
console.log("- Check Network tab for API calls to /api/ai-response");
console.log("- Verify the conversation history is being passed correctly");

export { testCases, mockConversationContext };
