import { NextResponse } from 'next/server';
import { getEmailService, createNewsletterNotificationEmail, createNewsletterConfirmationEmail } from '@/lib/email-service';

export async function GET() {
  return NextResponse.json({ message: 'Newsletter API endpoint. Use POST to subscribe.' });
}

export async function POST(request: Request) {
  const { email, locale } = await request.json();

  // Basic validation
  if (!email || !/\S+@\S+\.\S+/.test(email)) {
    return NextResponse.json(
      { error: 'Valid email is required' },
      { status: 400 }
    );
  }

  try {
    const emailService = getEmailService();
    const userLocale = locale || 'en';

    // Load translations for email subjects
    const messages = await import(`../../../messages/${userLocale}.json`);
    const t = messages.default.EmailTemplates;

    // Create notification email for admin team
    const notificationHtml = await createNewsletterNotificationEmail(email, userLocale);

    // Create confirmation email for subscriber
    const confirmationHtml = await createNewsletterConfirmationEmail(userLocale);

    // Send both emails
    await emailService.sendMultipleEmails([
      {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: t.newsletter.notificationSubject,
        html: notificationHtml
      },
      {
        to: email,
        subject: t.newsletter.confirmationSubject,
        html: confirmationHtml
      }
    ]);

    return NextResponse.json({
      message: 'Subscription successful',
      success: true
    });
  } catch (error) {
    console.error('Error processing newsletter subscription:', error);
    return NextResponse.json(
      {
        error: 'Failed to process subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
